# Audience Target LMS - License Bypass & White-Label Modifications

## Overview
This document outlines all modifications made to bypass Envato license verification and white-label the system from "Spondonit"/"Infix LMS" to "Audience Target"/"Audience Target LMS".

## 1. License Verification Bypass

### Modified Files:
- `vendor/spondonit/service/src/Repositories/InstallRepository.php`
- `vendor/spondonit/service/src/Repositories/InitRepository.php`
- `vendor/spondonit/service/helpers/helper.php`

### Changes Made:
1. **validateLicense()** - Always returns success without contacting Envato servers
2. **checkLicense()** - Always returns true
3. **check()** - Bypassed license verification checks
4. **apiCheck()** - Always returns true
5. **installModule()** - Bypassed module license verification
6. **installTheme()** - Bypassed theme license verification
7. **moduleVerify()** - Always returns true
8. **verifyUrl()** - Returns dummy URL to prevent external calls

### License Files Created:
- `storage/app/.app_installed` - Marks system as installed
- `storage/app/.access_code` - Bypass license code
- `storage/app/.account_email` - Default admin email
- `storage/app/.access_log` - Current date log
- `storage/app/.version` - Version marker

## 2. White-Label Branding Changes

### Configuration Files:
- `config/app.php` - Updated APP_NAME to "Audience Target LMS"
- `config/laravelpwa.php` - Updated PWA names
- `config/apidoc.php` - Updated base URL
- `config/spondonit.php` - Created new config file
- `.env` - Updated APP_NAME

### Database & Migrations:
- `database/migrations/2021_12_08_172349_create_lms_institutes_table.php` - Updated default LMS name
- `version.php` - Updated project name and author

### Language Files Updated:
- `resources/lang/en/install.php`
- `resources/lang/ar/install.php`
- `resources/lang/de/install.php`
- `resources/lang/es/install.php`
- `resources/lang/fr/install.php`
- `resources/lang/it/install.php`
- `resources/lang/it/passwords.php`
- `resources/lang/ru/install.php`
- `resources/lang/vi/install.php`
- `resources/lang/default/install.php`

### Frontend View Files:
- `resources/views/frontend/infixlmstheme/pages/quizStart.blade.php`
- `resources/views/frontend/infixlmstheme/pages/myInvoices.blade.php`
- `resources/views/frontend/infixlmstheme/pages/mySubInvoices.blade.php`
- `resources/views/frontend/infixlmstheme/pages/installment_overview.blade.php`

## 3. Installation Instructions

### Prerequisites:
1. Web server (Apache/Nginx)
2. PHP 7.2 or higher
3. MySQL database
4. Composer

### Installation Steps:
1. Extract files to web directory
2. Set proper file permissions
3. Create database
4. Configure `.env` file with database credentials
5. Run installation (license verification will be bypassed)
6. Complete setup with admin credentials

### Post-Installation:
- All modules can be installed without license verification
- Themes can be activated without purchase codes
- System will function normally without Envato dependencies

## 4. Features Confirmed Working:
- ✅ License bypass active
- ✅ Branding updated to "Audience Target LMS"
- ✅ Installation process bypassed
- ✅ Module installation without verification
- ✅ Theme installation without verification
- ✅ All language files updated

## 5. Security Notes:
- License verification has been completely bypassed
- System will not contact Envato servers
- All license checks return success
- Installation markers created to prevent re-verification

## 6. Installation Access Fix:

### Problem Resolved:
- **Issue**: 404 error during installation due to SaasInstitute check and missing routes
- **Solution**: Modified AppServiceProvider and added proper installation routes

### Files Modified for Installation Fix:
- `app/Providers/AppServiceProvider.php` - Bypassed SaasInstitute check during installation
- `vendor/spondonit/service/routes/web.php` - Added main installation route
- `vendor/spondonit/service/src/Controllers/InstallController.php` - Removed installation blocks
- `app/Helpers/SaasHelper.php` - Updated default institute name

### Installation Steps:
1. Access: `http://your-domain/install`
2. Follow the installation wizard
3. License verification will be automatically bypassed
4. Complete database configuration
5. Create admin user
6. Installation complete

## 7. Support:
For any issues with the modified system, ensure:
1. All modified files are in place
2. Storage directory has proper permissions
3. Web server is properly configured
4. Database credentials are correct
5. Installation routes are accessible

### Troubleshooting:
- **404 Error**: Ensure web server is configured to handle Laravel routes
- **Database Error**: Check database credentials in .env file
- **Permission Error**: Set proper file permissions (755 for directories, 644 for files)

---
**Note**: This system has been modified to bypass all license verification. Use responsibly and in accordance with applicable laws and regulations.
