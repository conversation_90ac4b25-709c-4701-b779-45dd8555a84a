<?php
/**
 * Test script to verify installation access
 * This script checks if the installation routes are accessible
 */

echo "=== Audience Target LMS Installation Access Test ===\n\n";

// Test 1: Check if installation routes are defined
echo "1. Checking installation route configuration:\n";

// Check if the service routes file exists and has the correct routes
$serviceRoutesFile = 'vendor/spondonit/service/routes/web.php';
if (file_exists($serviceRoutesFile)) {
    $routeContent = file_get_contents($serviceRoutesFile);
    if (strpos($routeContent, "Route::get('install'") !== false) {
        echo "   ✓ Main installation route found\n";
    } else {
        echo "   ✗ Main installation route missing\n";
    }
    
    if (strpos($routeContent, "service.preRequisite") !== false) {
        echo "   ✓ Pre-requisite route found\n";
    } else {
        echo "   ✗ Pre-requisite route missing\n";
    }
    
    if (strpos($routeContent, "service.license") !== false) {
        echo "   ✓ License route found\n";
    } else {
        echo "   ✗ License route missing\n";
    }
} else {
    echo "   ✗ Service routes file not found\n";
}

// Test 2: Check if AppServiceProvider has been modified
echo "\n2. Checking AppServiceProvider modifications:\n";
$appServiceProvider = 'app/Providers/AppServiceProvider.php';
if (file_exists($appServiceProvider)) {
    $content = file_get_contents($appServiceProvider);
    if (strpos($content, "!request()->is('install*')") !== false) {
        echo "   ✓ Installation bypass added to AppServiceProvider\n";
    } else {
        echo "   ✗ Installation bypass missing from AppServiceProvider\n";
    }
} else {
    echo "   ✗ AppServiceProvider not found\n";
}

// Test 3: Check if InstallController has been modified
echo "\n3. Checking InstallController modifications:\n";
$installController = 'vendor/spondonit/service/src/Controllers/InstallController.php';
if (file_exists($installController)) {
    $content = file_get_contents($installController);
    if (strpos($content, "Allow access to installation even if already installed") !== false) {
        echo "   ✓ Installation access bypass added\n";
    } else {
        echo "   ✗ Installation access bypass missing\n";
    }
    
    if (strpos($content, "public function user()") !== false) {
        echo "   ✓ User creation method found\n";
    } else {
        echo "   ✗ User creation method missing\n";
    }
} else {
    echo "   ✗ InstallController not found\n";
}

// Test 4: Check license bypass
echo "\n4. Checking license bypass:\n";
$installRepo = 'vendor/spondonit/service/src/Repositories/InstallRepository.php';
if (file_exists($installRepo)) {
    $content = file_get_contents($installRepo);
    if (strpos($content, "Bypass license validation") !== false) {
        echo "   ✓ License validation bypass found\n";
    } else {
        echo "   ✗ License validation bypass missing\n";
    }
} else {
    echo "   ✗ InstallRepository not found\n";
}

// Test 5: Check branding updates
echo "\n5. Checking branding updates:\n";
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    if (strpos($env, 'Audience Target LMS') !== false) {
        echo "   ✓ App name updated in .env\n";
    } else {
        echo "   ✗ App name not updated in .env\n";
    }
} else {
    echo "   ✗ .env file not found\n";
}

$saasHelper = 'app/Helpers/SaasHelper.php';
if (file_exists($saasHelper)) {
    $content = file_get_contents($saasHelper);
    if (strpos($content, "Audience Target LMS") !== false) {
        echo "   ✓ Branding updated in SaasHelper\n";
    } else {
        echo "   ✗ Branding not updated in SaasHelper\n";
    }
} else {
    echo "   ✗ SaasHelper not found\n";
}

echo "\n=== Test Complete ===\n";
echo "If all tests show ✓ (checkmarks), the installation should now be accessible.\n";
echo "Try accessing: http://your-domain/install\n";
echo "The system should now bypass license verification and allow installation.\n";
?>
