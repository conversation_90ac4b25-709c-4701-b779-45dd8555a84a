<?php
/**
 * Test script to verify license bypass functionality
 * Run this script to check if the license verification has been successfully bypassed
 */

// Include the helper functions
require_once 'vendor/spondonit/service/helpers/helper.php';

echo "=== Audience Target LMS License Bypass Test ===\n\n";

// Test 1: Check if isTestMode returns true or if we're in bypass mode
echo "1. Testing isTestMode function:\n";
if (function_exists('isTestMode')) {
    $testMode = isTestMode();
    echo "   isTestMode(): " . ($testMode ? 'true' : 'false') . "\n";
} else {
    echo "   isTestMode function not found\n";
}

// Test 2: Check verifyUrl function
echo "\n2. Testing verifyUrl function:\n";
if (function_exists('verifyUrl')) {
    $url = verifyUrl();
    echo "   verifyUrl(): $url\n";
    echo "   " . ($url === 'http://localhost/bypass' ? 'PASS - Bypass URL detected' : 'FAIL - Original URL still active') . "\n";
} else {
    echo "   verifyUrl function not found\n";
}

// Test 3: Check if license files exist
echo "\n3. Checking license bypass files:\n";
$files = [
    '.app_installed' => 'Installation marker',
    '.access_code' => 'Access code',
    '.account_email' => 'Account email',
    '.access_log' => 'Access log',
    '.version' => 'Version file'
];

foreach ($files as $file => $description) {
    $path = "storage/app/$file";
    if (file_exists($path)) {
        $content = trim(file_get_contents($path));
        echo "   ✓ $description ($file): $content\n";
    } else {
        echo "   ✗ $description ($file): NOT FOUND\n";
    }
}

// Test 4: Check configuration
echo "\n4. Testing configuration:\n";
if (file_exists('config/spondonit.php')) {
    echo "   ✓ Spondonit config file created\n";
    $config = include 'config/spondonit.php';
    if (isset($config['license_bypass']) && $config['license_bypass']) {
        echo "   ✓ License bypass enabled in config\n";
    } else {
        echo "   ✗ License bypass not enabled in config\n";
    }
} else {
    echo "   ✗ Spondonit config file not found\n";
}

// Test 5: Check .env file
echo "\n5. Checking .env configuration:\n";
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    if (strpos($env, 'APP_NAME="Audience Target LMS"') !== false) {
        echo "   ✓ App name updated to 'Audience Target LMS'\n";
    } else {
        echo "   ✗ App name not updated in .env\n";
    }
} else {
    echo "   ✗ .env file not found\n";
}

echo "\n=== Test Complete ===\n";
echo "If all tests show ✓ (checkmarks), the license bypass should be working correctly.\n";
echo "You can now proceed with the installation without requiring Envato credentials.\n";
?>
